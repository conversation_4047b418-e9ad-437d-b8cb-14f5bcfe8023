apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "chatgpt-next-web.fullname" . }}
  labels:
    {{- include "chatgpt-next-web.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "chatgpt-next-web.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "chatgpt-next-web.selectorLabels" . | nindent 8 }}
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default "latest" }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.service.port }}
              protocol: TCP
          env:
            - name: CUSTOM_MODELS
              value: {{ .Values.config.customModels | quote }}
            - name: DEFAULT_MODEL
              value: {{ .Values.config.defaultModel | quote }}
            - name: BASE_URL
              value: {{ .Values.config.baseUrl | quote }}
            - name: OPENAI_API_KEY
              valueFrom:
                secretKeyRef:
                  name: {{ include "chatgpt-next-web.fullname" . }}
                  key: api-key
            - name: DISABLE_GPT3
              value: {{ .Values.config.disableGpt3 | quote }}
            - name: DISABLE_GPT4
              value: {{ .Values.config.disableGpt4 | quote }}
            - name: ENABLE_BALANCE_QUERY
              value: {{ .Values.config.enableBalanceQuery | quote }}
            - name: DISABLE_MODEL_SWITCH
              value: {{ .Values.config.disableModelSwitch | quote }}
            - name: MASK_MODEL
              value: {{ .Values.config.maskModel | quote }}
            - name: CODE
              value: {{ .Values.config.code | quote }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          livenessProbe:
            httpGet:
              path: /
              port: {{ .Values.service.port }}
            {{- toYaml .Values.probes.liveness | nindent 12 }}
          readinessProbe:
            httpGet:
              path: /
              port: {{ .Values.service.port }}
            {{- toYaml .Values.probes.readiness | nindent 12 }}
