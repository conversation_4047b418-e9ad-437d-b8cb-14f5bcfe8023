#!/bin/bash

echo "🧪 测试所有镜像源的完整路径重写功能"
echo "================================================"

NGINX_POD=$(kubectl get pods -n zot -l app=nginx-proxy -o jsonpath='{.items[0].metadata.name}')
echo "使用 nginx pod: $NGINX_POD"
echo ""

# 测试函数
test_registry_path() {
    local path="$1"
    local description="$2"
    echo "🔍 测试: $description"
    echo "   路径: $path"
    
    result=$(kubectl exec -n zot $NGINX_POD -- curl -s -o /dev/null -w "%{http_code}" "http://localhost$path")
    
    if [ "$result" = "200" ]; then
        echo "   ✅ 成功 (HTTP $result)"
    else
        echo "   ❌ 失败 (HTTP $result)"
    fi
    echo ""
}

# 测试所有镜像源的完整路径
echo "🌐 测试各大镜像源的 Docker Registry API v2 端点:"
echo ""

test_registry_path "/registry.k8s.io/v2/" "Kubernetes 官方镜像 (registry.k8s.io)"
test_registry_path "/k8s.gcr.io/v2/" "Kubernetes 镜像兼容 (k8s.gcr.io)"  
test_registry_path "/gcr.io/v2/" "Google Container Registry (gcr.io)"
test_registry_path "/quay.io/v2/" "Red Hat Quay (quay.io)"
test_registry_path "/ghcr.io/v2/" "GitHub Container Registry (ghcr.io)"
test_registry_path "/mcr.microsoft.com/v2/" "Microsoft Container Registry (mcr.microsoft.com)"
test_registry_path "/docker.io/v2/" "Docker Hub (docker.io)"

echo "🎯 测试具体镜像的 manifest 请求:"
echo ""

# 测试具体镜像的manifest
kubectl exec -n zot $NGINX_POD -- curl -s "http://localhost/registry.k8s.io/v2/sig-storage/csi-provisioner/manifests/v3.4.0" \
    -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
    -o /dev/null -w "registry.k8s.io CSI Provisioner: HTTP %{http_code}\n"

kubectl exec -n zot $NGINX_POD -- curl -s "http://localhost/mcr.microsoft.com/v2/dotnet/runtime/manifests/8.0" \
    -H "Accept: application/vnd.docker.distribution.manifest.v2+json" \
    -o /dev/null -w "mcr.microsoft.com .NET Runtime: HTTP %{http_code}\n"

echo ""
echo "✅ 路径重写测试完成！"
echo ""
echo "📋 现在支持的完整路径格式:"
echo "   docker pull docker.at9.net/registry.k8s.io/sig-storage/csi-provisioner:v3.4.0"
echo "   docker pull docker.at9.net/mcr.microsoft.com/dotnet/runtime:8.0"
echo "   docker pull docker.at9.net/gcr.io/distroless/static:latest"
echo "   docker pull docker.at9.net/quay.io/prometheus/prometheus:latest"
echo "   docker pull docker.at9.net/ghcr.io/user/repo:tag"
echo "   docker pull docker.at9.net/k8s.gcr.io/pause:3.6"
echo "   docker pull docker.at9.net/docker.io/nginx:latest" 