# Default values for chatgpt-next-web
replicaCount: 1

image:
  repository: yidadaa/chatgpt-next-web
  tag: latest
  pullPolicy: IfNotPresent

nameOverride: ""
fullnameOverride: ""

serviceAccount:
  create: false
  name: ""

podAnnotations: {}

service:
  type: ClusterIP
  port: 3000

resources:
  limits:
    memory: 1Gi
  requests:
    memory: 512Mi

# 应用配置
config:
  customModels: "+grok-beta"
  defaultModel: "grok-beta"
  baseUrl: "https://api.x.ai"
  disableGpt3: "true"
  disableGpt4: "true"
  enableBalanceQuery: "false"
  disableModelSwitch: "true"
  maskModel: "grok-beta"
  code: "at9"

# 探针配置
probes:
  liveness:
    initialDelaySeconds: 40
    periodSeconds: 60
    timeoutSeconds: 10
    failureThreshold: 3
  readiness:
    initialDelaySeconds: 40
    periodSeconds: 60
    timeoutSeconds: 10
    failureThreshold: 3

# Secret配置
secret:
  create: true
  name: chatgpt-next-web-secret
  apiKey: "************************************************************************************"

# Ingress配置
ingress:
  enabled: true
  className: "traefik"
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  host: chat.at9.net
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local
