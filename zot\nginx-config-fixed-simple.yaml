apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config-fixed
  namespace: zot
data:
  nginx.conf: |
    events {
        worker_connections 1024;
    }
    
    http {
        upstream zot_backend {
            server zot.zot.svc.cluster.local:5000;
        }
        
        map $request_uri $registry_source {
            ~^/v2/docker\.io/(.*)$           "docker.io";
            ~^/v2/registry\.k8s\.io/(.*)$    "registry.k8s.io";
            ~^/v2/k8s\.gcr\.io/(.*)$         "k8s.gcr.io";
            ~^/v2/gcr\.io/(.*)$              "gcr.io";
            ~^/v2/quay\.io/(.*)$             "quay.io";
            ~^/v2/ghcr\.io/(.*)$             "ghcr.io";
            ~^/v2/mcr\.microsoft\.com/(.*)$  "mcr.microsoft.com";
            default                          "docker.io";
        }
        
        server {
            listen 80;
            server_name _;
            
            # 健康检查和首页
            location = / {
                return 200 'Zot Registry Mirror - Working!';
                add_header Content-Type text/plain;
            }
            
            # Docker Registry API v2 根端点 - 必须保持
            location = /v2/ {
                proxy_pass http://zot_backend/v2/;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # === 关键修复：Docker.io 完整路径重写 ===
            location ~ ^/v2/docker\.io/(.*)$ {
                rewrite ^/v2/docker\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "docker.io";
            }
            
            # === registry.k8s.io 完整路径重写 ===
            location ~ ^/v2/registry\.k8s\.io/(.*)$ {
                rewrite ^/v2/registry\.k8s\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "registry.k8s.io";
            }
            
            # === k8s.gcr.io 完整路径重写 ===
            location ~ ^/v2/k8s\.gcr\.io/(.*)$ {
                rewrite ^/v2/k8s\.gcr\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "k8s.gcr.io";
            }
            
            # === gcr.io 完整路径重写 ===
            location ~ ^/v2/gcr\.io/(.*)$ {
                rewrite ^/v2/gcr\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "gcr.io";
            }
            
            # === quay.io 完整路径重写 ===
            location ~ ^/v2/quay\.io/(.*)$ {
                rewrite ^/v2/quay\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "quay.io";
            }
            
            # === ghcr.io 完整路径重写 ===
            location ~ ^/v2/ghcr\.io/(.*)$ {
                rewrite ^/v2/ghcr\.io/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "ghcr.io";
            }
            
            # === mcr.microsoft.com 完整路径重写 ===
            location ~ ^/v2/mcr\.microsoft\.com/(.*)$ {
                rewrite ^/v2/mcr\.microsoft\.com/(.*)$ /v2/$1 break;
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "mcr.microsoft.com";
            }
            
            # 默认路径处理（Docker Hub 和其他直接路径）
            location /v2/ {
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_set_header X-Registry-Source "docker.io";
            }
            
            # 处理非 v2 API 请求
            location / {
                proxy_pass http://zot_backend;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
        }
    }
} 