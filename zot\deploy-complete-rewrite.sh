#!/bin/bash

echo "🚀 部署完整路径重写配置 v3.0"
echo "支持的镜像源: docker.io, gcr.io, registry.k8s.io, k8s.gcr.io, quay.io, ghcr.io, mcr.microsoft.com"

# 1. 应用新的 nginx 配置
echo "1️⃣ 应用完整版 nginx 配置..."
kubectl apply -f nginx-config-complete.yaml

# 2. 等待配置更新
echo "2️⃣ 等待 ConfigMap 更新..."
sleep 3

# 3. 强制重启 nginx-proxy pod（确保配置生效）
echo "3️⃣ 强制重启 nginx-proxy pod..."
kubectl delete pod -n zot -l app=nginx-proxy
sleep 5

# 4. 等待新 pod 启动
echo "4️⃣ 等待新 nginx-proxy pod 启动..."
kubectl wait --for=condition=ready pod -n zot -l app=nginx-proxy --timeout=60s

# 5. 检查 pod 状态
echo "5️⃣ 检查服务状态:"
kubectl get pods -n zot
echo ""

# 6. 获取 nginx pod 名称并测试内部配置
NGINX_POD=$(kubectl get pods -n zot -l app=nginx-proxy -o jsonpath='{.items[0].metadata.name}')
echo "📋 检查 nginx 配置是否正确加载..."
kubectl exec -n zot $NGINX_POD -- nginx -t

# 7. 内部测试路径重写功能
echo ""
echo "🧪 内部测试路径重写功能..."

# 测试 registry.k8s.io 路径重写
echo "测试 registry.k8s.io 路径重写:"
kubectl exec -n zot $NGINX_POD -- curl -s -I "http://localhost/registry.k8s.io/v2/" | head -n 1

# 测试 mcr.microsoft.com 路径重写  
echo "测试 mcr.microsoft.com 路径重写:"
kubectl exec -n zot $NGINX_POD -- curl -s -I "http://localhost/mcr.microsoft.com/v2/" | head -n 1

# 测试 gcr.io 路径重写
echo "测试 gcr.io 路径重写:"
kubectl exec -n zot $NGINX_POD -- curl -s -I "http://localhost/gcr.io/v2/" | head -n 1

# 测试 ghcr.io 路径重写
echo "测试 ghcr.io 路径重写:"
kubectl exec -n zot $NGINX_POD -- curl -s -I "http://localhost/ghcr.io/v2/" | head -n 1

echo ""
echo "✅ 部署完成！现在支持所有 7 个镜像源的完整路径访问："
echo "   📦 Docker Hub: docker.at9.net/docker.io/nginx:latest"
echo "   🏗️ Google GCR: docker.at9.net/gcr.io/distroless/static:latest"  
echo "   ☸️ Kubernetes: docker.at9.net/registry.k8s.io/pause:3.9"
echo "   🔄 K8s Legacy: docker.at9.net/k8s.gcr.io/pause:3.6"
echo "   🔴 Red Hat Quay: docker.at9.net/quay.io/prometheus/prometheus:latest"
echo "   🐙 GitHub: docker.at9.net/ghcr.io/user/repo:tag"
echo "   🟦 Microsoft: docker.at9.net/mcr.microsoft.com/dotnet/runtime:8.0"
echo ""
echo "🎯 立即测试："
echo "   docker pull docker.at9.net/registry.k8s.io/sig-storage/csi-provisioner:v3.4.0" 