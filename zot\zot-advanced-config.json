{"distSpecVersion": "1.1.0-dev", "storage": {"rootDirectory": "/var/lib/registry", "gc": true, "gcInterval": "24h", "dedupe": true, "gcReferrers": true, "subPaths": {"/registry.k8s.io": {"rootDirectory": "/var/lib/registry/k8s", "dedupe": false}, "/gcr.io": {"rootDirectory": "/var/lib/registry/gcr", "dedupe": false}, "/quay.io": {"rootDirectory": "/var/lib/registry/quay", "dedupe": false}, "/ghcr.io": {"rootDirectory": "/var/lib/registry/ghcr", "dedupe": false}, "/mcr.microsoft.com": {"rootDirectory": "/var/lib/registry/mcr", "dedupe": false}}}, "http": {"address": "0.0.0.0", "port": "5000", "realm": "zot", "tls": {"cert": "", "key": ""}}, "log": {"level": "debug", "output": "/tmp/zot.log"}, "extensions": {"sync": {"enable": true, "credentialsFile": "", "registries": [{"urls": ["https://registry-1.docker.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://registry.k8s.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "destination": "/registry.k8s.io", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://gcr.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "destination": "/gcr.io", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://quay.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "destination": "/quay.io", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://ghcr.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "destination": "/ghcr.io", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://mcr.microsoft.com"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 5, "retryDelay": "5m", "pollInterval": "6h", "content": [{"prefix": "**", "destination": "/mcr.microsoft.com", "tags": {"regex": ".*", "semver": true}}]}]}, "ui": {"enable": true}, "search": {"enable": true, "cve": {"updateInterval": "2h"}}, "lint": {"enable": true, "mandatoryAnnotations": ["org.opencontainers.image.source"]}}}