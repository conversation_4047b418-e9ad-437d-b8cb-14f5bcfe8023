#!/bin/bash

echo "🧪 测试完整路径访问功能..."
echo "======================================"

# 定义测试用例
declare -a tests=(
    "docker.at9.net/docker.io/nginx:latest|Docker Hub 完整路径"
    "docker.at9.net/registry.k8s.io/pause:3.9|Kubernetes 官方镜像"
    "docker.at9.net/k8s.gcr.io/pause:3.6|Kubernetes 兼容镜像"
    "docker.at9.net/gcr.io/distroless/static:latest|Google Container Registry"
    "docker.at9.net/quay.io/prometheus/prometheus:latest|Red Hat Quay"
    "docker.at9.net/ghcr.io/nginxinc/nginx-unprivileged:latest|GitHub Container Registry"
    "docker.at9.net/mcr.microsoft.com/dotnet/runtime:8.0|Microsoft Container Registry"
)

# 测试结果计数器
success_count=0
total_count=${#tests[@]}

# 执行测试
for test_case in "${tests[@]}"; do
    IFS='|' read -r image_url description <<< "$test_case"
    
    echo ""
    echo "🔍 测试: $description"
    echo "📦 镜像: $image_url"
    
    # 测试镜像信息获取
    if docker manifest inspect "$image_url" >/dev/null 2>&1; then
        echo "✅ 成功: 可以获取镜像信息"
        ((success_count++))
    else
        echo "❌ 失败: 无法获取镜像信息"
        echo "🔧 建议检查: nginx 配置中对应的路径重写规则"
    fi
done

echo ""
echo "======================================"
echo "📊 测试结果汇总:"
echo "✅ 成功: $success_count/$total_count"
echo "❌ 失败: $((total_count - success_count))/$total_count"

if [ $success_count -eq $total_count ]; then
    echo "🎉 所有测试通过！完整路径访问功能正常"
    exit 0
else
    echo "⚠️  有测试失败，需要检查配置"
    exit 1
fi 