{"distSpecVersion": "1.1.0-dev", "storage": {"rootDirectory": "/var/lib/registry", "gc": true, "gcInterval": "24h", "dedupe": true, "gcReferrers": true}, "http": {"address": "0.0.0.0", "port": "5000", "realm": "zot", "tls": {"cert": "", "key": ""}}, "log": {"level": "info", "output": "/tmp/zot.log"}, "extensions": {"sync": {"enable": true, "credentialsFile": "", "registries": [{"urls": ["https://registry-1.docker.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://registry.k8s.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://gcr.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://quay.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://ghcr.io"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}, {"urls": ["https://mcr.microsoft.com"], "onDemand": true, "tlsVerify": true, "onlySigned": false, "maxRetries": 3, "retryDelay": "5m", "content": [{"prefix": "**", "tags": {"regex": ".*", "semver": true}}]}]}, "ui": {"enable": true}, "search": {"enable": true, "cve": {"updateInterval": "2h"}}, "lint": {"enable": true, "mandatoryAnnotations": ["org.opencontainers.image.source"]}}}