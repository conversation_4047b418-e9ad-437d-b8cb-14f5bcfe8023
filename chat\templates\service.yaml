apiVersion: v1
kind: Service
metadata:
  name: {{ include "chatgpt-next-web.fullname" . }}
  labels:
    {{- include "chatgpt-next-web.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
  selector:
    {{- include "chatgpt-next-web.selectorLabels" . | nindent 4 }}
