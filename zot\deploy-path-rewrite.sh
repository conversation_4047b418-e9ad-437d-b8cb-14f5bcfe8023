#!/bin/bash

# Zot 路径重写配置部署脚本
# 解决完整路径（如 docker.at9.net/registry.k8s.io/xxx）无法访问的问题

echo "🚀 开始部署 Zot 路径重写配置..."

# 1. 备份现有配置
echo "📦 备份现有配置..."
kubectl get configmap nginx-config -n zot -o yaml > nginx-config-backup-$(date +%Y%m%d-%H%M%S).yaml 2>/dev/null || echo "无现有配置需要备份"

# 2. 应用新的 Nginx 配置（支持路径重写）
echo "🔧 应用新的 Nginx 配置（支持路径重写）..."
kubectl apply -f nginx-config-with-rewrite.yaml

# 3. 重启 Nginx Pod 使配置生效
echo "🔄 重启 Nginx Pod..."
kubectl rollout restart deployment/nginx -n zot 2>/dev/null || kubectl delete pods -l app=nginx -n zot

# 4. 等待 Pod 就绪
echo "⏳ 等待服务就绪..."
kubectl wait --for=condition=ready pod -l app=nginx -n zot --timeout=60s

# 5. 验证配置
echo "✅ 验证新配置..."
echo ""
echo "📋 现在支持的路径格式："
echo "  ✅ 完整路径: docker pull docker.at9.net/registry.k8s.io/sig-storage/csi-provisioner:v3.5.0"
echo "  ✅ 简化路径: docker pull docker.at9.net/sig-storage/csi-provisioner:v3.5.0"
echo ""
echo "🌐 访问 Web 界面查看更新："
echo "  https://docker.at9.net"
echo ""
echo "🧪 测试命令："
echo "  curl -I https://docker.at9.net/v2/"
echo "  curl -I https://docker.at9.net/registry.k8s.io/v2/"
echo ""

# 6. 可选：应用 Zot 高级配置
read -p "❓ 是否也要应用 Zot 高级配置？(y/N) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🔧 应用 Zot 高级配置..."
    
    # 创建 Zot 配置的 ConfigMap
    kubectl create configmap zot-config --from-file=config.json=zot-advanced-config.json -n zot --dry-run=client -o yaml | kubectl apply -f -
    
    # 重启 Zot Pod
    kubectl rollout restart deployment/zot -n zot 2>/dev/null || kubectl delete pods -l app=zot -n zot
    
    echo "✅ Zot 高级配置已应用"
fi

echo ""
echo "🎉 部署完成！您的镜像仓库现在支持完整路径访问了！"
echo ""
echo "📝 主要改进："
echo "  • 智能路径重写：/registry.k8s.io/xxx -> /xxx"
echo "  • 自动上游路由：根据路径前缀选择对应镜像源"
echo "  • 向后兼容：原有简化路径依然可用"
echo "  • 透明代理：用户无感知的智能切换" 