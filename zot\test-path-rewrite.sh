#!/bin/bash

echo "🧪 测试 Zot 路径重写功能..."
echo ""

# 服务地址
SERVICE_URL="https://docker.at9.net"
POD_NAME=$(kubectl get pods -n zot -l app=nginx-proxy -o jsonpath='{.items[0].metadata.name}')

echo "📍 测试目标: $SERVICE_URL"
echo "🔗 Nginx Pod: $POD_NAME"
echo ""

# 测试函数
test_path() {
    local path="$1"
    local description="$2"
    
    echo "🔍 测试: $description"
    echo "    路径: $path"
    
    # 内部测试（通过 kubectl exec）
    local status_code=$(kubectl exec $POD_NAME -n zot -- curl -s -o /dev/null -w "%{http_code}" "http://localhost$path" 2>/dev/null)
    
    if [ "$status_code" = "200" ]; then
        echo "    ✅ 内部测试通过 (HTTP $status_code)"
    else
        echo "    ❌ 内部测试失败 (HTTP $status_code)"
    fi
    
    echo ""
}

# 测试路径重写
echo "🎯 测试 registry.k8s.io 路径重写:"
test_path "/registry.k8s.io/v2/sig-storage/csi-provisioner/manifests/v3.5.0" "K8s 镜像 (带 v2 前缀)"
test_path "/registry.k8s.io/sig-storage/csi-provisioner/manifests/v3.5.0" "K8s 镜像 (不带 v2 前缀)"

echo "🎯 测试其他镜像源路径:"
test_path "/gcr.io/v2/distroless/static-debian11/manifests/latest" "GCR 镜像 (带 v2 前缀)"
test_path "/gcr.io/distroless/static-debian11/manifests/latest" "GCR 镜像 (不带 v2 前缀)"

echo "🎯 测试基础 API 端点:"
test_path "/v2/" "Docker Registry API v2 根端点"
test_path "/v2/sig-storage/csi-provisioner/manifests/v3.5.0" "标准镜像 (简化路径)"

echo ""
echo "🌐 现在您可以测试外部访问:"
echo "  curl -I $SERVICE_URL/registry.k8s.io/v2/sig-storage/csi-provisioner/manifests/v3.5.0"
echo "  docker pull $SERVICE_URL/registry.k8s.io/sig-storage/csi-provisioner:v3.5.0"
echo ""
echo "🎉 测试完成！" 