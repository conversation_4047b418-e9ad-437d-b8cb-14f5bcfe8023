root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# helm install prometheus-stack prometheus-community/kube-prometheus-stack -f my-values.yaml  -n monitor








NAME: prometheus-stack
LAST DEPLOYED: Tue <PERSON> 18 15:03:27 2025
NAMESPACE: monitor
STATUS: deployed
REVISION: 1
NOTES:
kube-prometheus-stack has been installed. Check its status by running:
  kubectl --namespace monitor get pods -l "release=prometheus-stack"

Get Grafana 'admin' user password by running:

  kubectl --namespace monitor get secrets prometheus-stack-grafana -o jsonpath="{.data.admin-password}" | base64 -d ; echo

Access Grafana local instance:

  export POD_NAME=$(kubectl --namespace monitor get pod -l "app.kubernetes.io/name=grafana,app.kubernetes.io/instance=prometheus-stack" -oname)
  kubectl --namespace monitor port-forward $POD_NAME 3000

Visit https://github.com/prometheus-operator/kube-prometheus for instructions on how to create & configure Alertmanager and Prometheus instances using the Operator.
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# ss -upntl
Netid   State    Recv-Q   Send-Q                    Local Address:Port        Peer Address:Port   Process                                      
udp     UNCONN   0        0                               0.0.0.0:8472             0.0.0.0:*                                                   
udp     UNCONN   0        0                               0.0.0.0:41641            0.0.0.0:*       users:(("tailscaled",pid=2708797,fd=10))    
udp     UNCONN   0        0                               0.0.0.0:25797            0.0.0.0:*       users:(("sing-box",pid=3310938,fd=7))       
udp     UNCONN   0        0                               0.0.0.0:13389            0.0.0.0:*       users:(("docker-proxy",pid=3148773,fd=4))   
udp     UNCONN   0        0                                  [::]:41641               [::]:*       users:(("tailscaled",pid=2708797,fd=18))    
udp     UNCONN   0        0                                  [::]:25797               [::]:*       users:(("sing-box",pid=3310938,fd=8))       
udp     UNCONN   0        0                                     *:11537                  *:*       users:(("sing-box",pid=3310938,fd=15))      
udp     UNCONN   0        0                                     *:20897                  *:*       users:(("sing-box",pid=3310938,fd=17))      
udp     UNCONN   0        0                                  [::]:13389               [::]:*       users:(("docker-proxy",pid=3148780,fd=4))   
tcp     LISTEN   0        32768                      100.107.9.74:60948            0.0.0.0:*       users:(("tailscaled",pid=2708797,fd=22))    
tcp     LISTEN   0        32768                         127.0.0.1:10010            0.0.0.0:*       users:(("containerd",pid=3108537,fd=164))   
tcp     LISTEN   0        32768                           0.0.0.0:13389            0.0.0.0:*       users:(("docker-proxy",pid=3148749,fd=4))   
tcp     LISTEN   0        32768                         127.0.0.1:10248            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=197))   
tcp     LISTEN   0        32768                         127.0.0.1:10249            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=217))   
tcp     LISTEN   0        32768                         127.0.0.1:10256            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=213))   
tcp     LISTEN   0        32768                         127.0.0.1:10257            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=159))   
tcp     LISTEN   0        32768                         127.0.0.1:10258            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=204))   
tcp     LISTEN   0        32768                         127.0.0.1:10259            0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=184))   
tcp     LISTEN   0        32768                           0.0.0.0:5678             0.0.0.0:*       users:(("docker-proxy",pid=1088829,fd=4))   
tcp     LISTEN   0        32768                           0.0.0.0:13306            0.0.0.0:*       users:(("docker-proxy",pid=1816512,fd=4))   
tcp     LISTEN   0        32768                         127.0.0.1:6444             0.0.0.0:*       users:(("k3s-server",pid=3108517,fd=18))    
tcp     LISTEN   0        128                             0.0.0.0:10056            0.0.0.0:*       users:(("sshd",pid=509,fd=4))               
tcp     LISTEN   0        32768                                 *:6443                   *:*       users:(("k3s-server",pid=3108517,fd=13))    
tcp     LISTEN   0        32768                                 *:47799                  *:*       users:(("sing-box",pid=3310938,fd=12))      
tcp     LISTEN   0        32768                              [::]:13389               [::]:*       users:(("docker-proxy",pid=3148765,fd=4))   
tcp     LISTEN   0        32768                              [::]:5678                [::]:*       users:(("docker-proxy",pid=1088836,fd=4))   
tcp     LISTEN   0        32768        [fd7a:115c:a1e0::4901:94a]:41186               [::]:*       users:(("tailscaled",pid=2708797,fd=23))    
tcp     LISTEN   0        32768                              [::]:13306               [::]:*       users:(("docker-proxy",pid=1816518,fd=4))   
tcp     LISTEN   6        32768                                 *:10260                  *:*       users:(("k3s-server",pid=3108517,fd=206))   
tcp     LISTEN   0        32768                                 *:2052                   *:*       users:(("sing-box",pid=3310938,fd=13))      
tcp     LISTEN   0        32768                                 *:10250                  *:*       users:(("k3s-server",pid=3108517,fd=193))   
tcp     LISTEN   0        128                                [::]:10056               [::]:*       users:(("sshd",pid=509,fd=3))               
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# ss -upntl | grep 9
udp   UNCONN 0      0                         0.0.0.0:41641      0.0.0.0:*    users:(("tailscaled",pid=2708797,fd=10)) 
udp   UNCONN 0      0                         0.0.0.0:25797      0.0.0.0:*    users:(("sing-box",pid=3310938,fd=7))    
udp   UNCONN 0      0                         0.0.0.0:13389      0.0.0.0:*    users:(("docker-proxy",pid=3148773,fd=4))
udp   UNCONN 0      0                            [::]:41641         [::]:*    users:(("tailscaled",pid=2708797,fd=18)) 
udp   UNCONN 0      0                            [::]:25797         [::]:*    users:(("sing-box",pid=3310938,fd=8))    
udp   UNCONN 0      0                               *:11537            *:*    users:(("sing-box",pid=3310938,fd=15))   
udp   UNCONN 0      0                               *:20897            *:*    users:(("sing-box",pid=3310938,fd=17))   
udp   UNCONN 0      0                            [::]:13389         [::]:*    users:(("docker-proxy",pid=3148780,fd=4))
tcp   LISTEN 0      32768                100.107.9.74:60948      0.0.0.0:*    users:(("tailscaled",pid=2708797,fd=22)) 
tcp   LISTEN 0      32768                     0.0.0.0:13389      0.0.0.0:*    users:(("docker-proxy",pid=3148749,fd=4))
tcp   LISTEN 0      32768                   127.0.0.1:10248      0.0.0.0:*    users:(("k3s-server",pid=3108517,fd=197))
tcp   LISTEN 0      32768                   127.0.0.1:10249      0.0.0.0:*    users:(("k3s-server",pid=3108517,fd=217))
tcp   LISTEN 0      32768                   127.0.0.1:10257      0.0.0.0:*    users:(("k3s-server",pid=3108517,fd=159))
tcp   LISTEN 0      32768                   127.0.0.1:10259      0.0.0.0:*    users:(("k3s-server",pid=3108517,fd=184))
tcp   LISTEN 0      32768                     0.0.0.0:5678       0.0.0.0:*    users:(("docker-proxy",pid=1088829,fd=4))
tcp   LISTEN 0      128                       0.0.0.0:10056      0.0.0.0:*    users:(("sshd",pid=509,fd=4))            
tcp   LISTEN 0      32768                           *:47799            *:*    users:(("sing-box",pid=3310938,fd=12))   
tcp   LISTEN 0      32768                        [::]:13389         [::]:*    users:(("docker-proxy",pid=3148765,fd=4))
tcp   LISTEN 0      32768  [fd7a:115c:a1e0::4901:94a]:41186         [::]:*    users:(("tailscaled",pid=2708797,fd=23)) 
tcp   LISTEN 0      32768                           *:2052             *:*    users:(("sing-box",pid=3310938,fd=13))   
tcp   LISTEN 0      32768                           *:10250            *:*    users:(("k3s-server",pid=3108517,fd=193))
tcp   LISTEN 0      128                          [::]:10056         [::]:*    users:(("sshd",pid=509,fd=3))            
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# kubectl get pods -A
NAMESPACE     NAME                                                     READY   STATUS      RESTARTS       AGE
kube-system   coredns-ccb96694c-ph2v9                                  1/1     Running     5 (34h ago)    42h
kube-system   helm-install-traefik-8t5n2                               0/1     Completed   1              42h
kube-system   helm-install-traefik-crd-zfnlz                           0/1     Completed   0              42h
kube-system   local-path-provisioner-5b5f758bcf-2657v                  1/1     Running     0              42h
kube-system   metrics-server-7bf7d58749-rf8zc                          1/1     Running     13 (31h ago)   42h
kube-system   svclb-traefik-92bba7d9-9hqk2                             2/2     Running     0              42h
kube-system   svclb-traefik-92bba7d9-l5cg2                             2/2     Running     0              41h
kube-system   svclb-traefik-92bba7d9-m959r                             2/2     Running     0              41h
kube-system   svclb-traefik-92bba7d9-n94w2                             2/2     Running     0              41h
kube-system   svclb-traefik-92bba7d9-qxvlg                             2/2     Running     0              42h
kube-system   svclb-traefik-92bba7d9-sv26r                             2/2     Running     0              41h
kube-system   traefik-5cbdcf97f4-p8r5l                                 1/1     Running     3 (33h ago)    42h
monitor       alertmanager-prometheus-stack-kube-prom-alertmanager-0   2/2     Running     0              92s
monitor       prometheus-prometheus-stack-kube-prom-prometheus-0       2/2     Running     0              87s
monitor       prometheus-stack-grafana-6748f64d69-qjgrw                3/3     Running     0              107s
monitor       prometheus-stack-kube-prom-operator-585b98cc85-w2qt2     1/1     Running     0              107s
monitor       prometheus-stack-kube-state-metrics-7c85b55cbb-m7hc8     1/1     Running     0              107s
monitor       prometheus-stack-prometheus-node-exporter-gms6z          1/1     Running     0              107s
monitor       prometheus-stack-prometheus-node-exporter-jvfgt          1/1     Running     0              107s
monitor       prometheus-stack-prometheus-node-exporter-pqqrs          1/1     Running     0              107s
monitor       prometheus-stack-prometheus-node-exporter-qr24p          0/1     Pending     0              107s
monitor       prometheus-stack-prometheus-node-exporter-tcc9f          1/1     Running     0              107s
monitor       prometheus-stack-prometheus-node-exporter-vg29c          1/1     Running     0              107s
root@5-10G-100G-Arizona-173-249-204-114:~/k8s/prometheus# 
