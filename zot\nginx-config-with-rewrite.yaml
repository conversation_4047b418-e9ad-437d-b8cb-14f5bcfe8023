apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config-with-rewrite
  namespace: zot
data:
  nginx.conf: |
    server {
        listen 80;
        server_name _;
        
        # 基础健康检查和首页
        location = / {
            root   /usr/share/nginx/html;
            try_files /index.html =404;
        }
        
        # Docker Registry API v2 endpoints
        location ~ ^/v2/$ {
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 处理完整路径：/registry.k8s.io/xxx -> /xxx (映射到 registry.k8s.io)
        location ~ ^/registry\.k8s\.io/(.*)$ {
            # 重写路径，去掉 registry.k8s.io 前缀
            rewrite ^/registry\.k8s\.io/(.*)$ /$1 break;
            
            # 添加自定义请求头，告知后端这是 k8s 镜像请求
            proxy_set_header X-Registry-Source "registry.k8s.io";
            proxy_set_header X-Original-URI $request_uri;
            
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 处理完整路径：/gcr.io/xxx -> /xxx (映射到 gcr.io)
        location ~ ^/gcr\.io/(.*)$ {
            rewrite ^/gcr\.io/(.*)$ /$1 break;
            proxy_set_header X-Registry-Source "gcr.io";
            proxy_set_header X-Original-URI $request_uri;
            
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 处理完整路径：/quay.io/xxx -> /xxx (映射到 quay.io)
        location ~ ^/quay\.io/(.*)$ {
            rewrite ^/quay\.io/(.*)$ /$1 break;
            proxy_set_header X-Registry-Source "quay.io";
            proxy_set_header X-Original-URI $request_uri;
            
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 处理完整路径：/ghcr.io/xxx -> /xxx (映射到 ghcr.io)
        location ~ ^/ghcr\.io/(.*)$ {
            rewrite ^/ghcr\.io/(.*)$ /$1 break;
            proxy_set_header X-Registry-Source "ghcr.io";
            proxy_set_header X-Original-URI $request_uri;
            
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 处理完整路径：/mcr.microsoft.com/xxx -> /xxx (映射到 mcr.microsoft.com)  
        location ~ ^/mcr\.microsoft\.com/(.*)$ {
            rewrite ^/mcr\.microsoft\.com/(.*)$ /$1 break;
            proxy_set_header X-Registry-Source "mcr.microsoft.com";
            proxy_set_header X-Original-URI $request_uri;
            
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 默认路径处理（Docker Hub 和其他）
        location / {
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Registry-Source "docker.io";
        }
    }
  index.html: |-
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Zot 公益镜像仓库 - 支持完整路径</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
                line-height: 1.6; color: #333;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 40px; color: white; }
            .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .header .subtitle { font-size: 1.2rem; opacity: 0.9; margin-bottom: 20px; }
            .header .description { font-size: 1rem; opacity: 0.8; max-width: 800px; margin: 0 auto; }
            .card { background: white; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px; }
            .section-title { font-size: 1.5rem; margin-bottom: 20px; color: #2c3e50; display: flex; align-items: center; gap: 10px; }
            .section-title::before { content: ''; width: 4px; height: 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 2px; }
            .highlight-box { background: #e8f5e8; border: 2px solid #4caf50; border-radius: 8px; padding: 20px; margin: 20px 0; }
            .highlight-box h3 { color: #2e7d32; margin-bottom: 10px; }
            .highlight-box p { margin-bottom: 10px; }
            .highlight-box code { background: #c8e6c9; padding: 4px 8px; border-radius: 4px; font-family: Monaco, Menlo, monospace; }
            .examples { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 15px; }
            .example-box { background: #f8f9fa; padding: 15px; border-radius: 6px; border-left: 4px solid #667eea; }
            .example-box strong { display: block; margin-bottom: 10px; color: #2c3e50; }
            .example-box code { display: block; margin-top: 5px; background: #e9ecef; padding: 8px; border-radius: 4px; font-family: Monaco, Menlo, monospace; font-size: 0.9em; }
            .footer { text-align: center; color: white; margin-top: 40px; opacity: 0.8; }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="header">
                <h1>🚀 Zot 公益镜像仓库</h1>
                <p class="subtitle">🌟 现在支持完整路径访问！</p>
                <p class="description">专为中国大陆开发者提供的容器镜像加速服务，现已支持官方完整路径访问</p>
            </header>
            
            <div class="highlight-box">
                <h3>🎉 重大更新：完整路径支持</h3>
                <p>现在您可以直接使用官方镜像的完整路径，无需删除前缀！</p>
                <p>例如：<code>docker pull docker.at9.net/registry.k8s.io/sig-storage/csi-provisioner:v3.5.0</code></p>
            </div>
            
            <div class="card">
                <h2 class="section-title">✅ 支持的完整路径格式</h2>
                <div class="examples">
                    <div class="example-box">
                        <strong>✅ 现在支持（完整路径）：</strong>
                        <code>docker pull docker.at9.net/registry.k8s.io/sig-storage/csi-provisioner:v3.5.0</code>
                        <code>docker pull docker.at9.net/gcr.io/distroless/static-debian11:latest</code>
                        <code>docker pull docker.at9.net/quay.io/prometheus/prometheus:latest</code>
                    </div>
                    <div class="example-box">
                        <strong>✅ 依然支持（简化路径）：</strong>
                        <code>docker pull docker.at9.net/sig-storage/csi-provisioner:v3.5.0</code>
                        <code>docker pull docker.at9.net/distroless/static-debian11:latest</code>
                        <code>docker pull docker.at9.net/prometheus/prometheus:latest</code>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h2 class="section-title">🔧 路径重写机制</h2>
                <p>我们的智能路径重写系统能够：</p>
                <ul style="margin-left: 20px; margin-top: 10px;">
                    <li>自动识别原始镜像仓库路径前缀</li>
                    <li>智能路由到对应的上游镜像源</li>
                    <li>保持与原有简化路径的完全兼容</li>
                    <li>提供透明的代理和缓存服务</li>
                </ul>
            </div>
            
            <footer class="footer">
                <p>Powered by <strong>Zot</strong> + <strong>Nginx 智能路由</strong> | 为开源社区提供免费服务</p>
            </footer>
        </div>
    </body>
    </html> 