apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: zot
data:
  nginx.conf: |
    server {
        listen 80;
        server_name _;
        location = / {
            root   /usr/share/nginx/html;
            try_files /index.html =404;
        }
        location / {
            proxy_pass http://zot.zot.svc.cluster.local:5000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
  index.html: |-
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Zot 公益镜像仓库</title>
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
                line-height: 1.6; color: #333;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 40px; color: white; }
            .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
            .header .subtitle { font-size: 1.2rem; opacity: 0.9; margin-bottom: 20px; }
            .header .description { font-size: 1rem; opacity: 0.8; max-width: 800px; margin: 0 auto; }
            .card { background: white; border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); padding: 30px; margin-bottom: 30px; }
            .section-title { font-size: 1.5rem; margin-bottom: 20px; color: #2c3e50; display: flex; align-items: center; gap: 10px; }
            .section-title::before { content: ''; width: 4px; height: 24px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 2px; }
            .service-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
            .info-item { padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #667eea; }
            .info-item strong { color: #2c3e50; }
            .info-item a { color: #667eea; text-decoration: none; }
            .info-item a:hover { text-decoration: underline; }
            .registry-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
            .registry-table th, .registry-table td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
            .registry-table th { background: #f8f9fa; font-weight: 600; color: #2c3e50; }
            .registry-table tr:hover { background: #f8f9fa; }
            .registry-table code { background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-family: Monaco, Menlo, monospace; font-size: 0.9em; }
            .usage-section { background: #2c3e50; color: white; border-radius: 8px; padding: 20px; margin-top: 20px; }
            .usage-title { font-size: 1.3rem; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
            .usage-simple { background: #3498db; padding: 15px; border-radius: 8px; margin-bottom: 15px; }
            .usage-simple h4 { margin-bottom: 10px; }
            .usage-simple p { opacity: 0.9; margin-bottom: 10px; }
            .usage-simple code { background: rgba(255,255,255,0.2); padding: 8px 12px; border-radius: 4px; display: block; margin-top: 10px; font-family: Monaco, Menlo, monospace; }
            .footer { text-align: center; color: white; margin-top: 40px; opacity: 0.8; }
            .examples { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 15px; }
            .example-box { background: rgba(255,255,255,0.1); padding: 15px; border-radius: 6px; }
            .example-box strong { display: block; margin-bottom: 10px; }
            .example-box code { display: block; margin-top: 5px; }
            @media (max-width: 768px) {
                .container { padding: 10px; }
                .header h1 { font-size: 2rem; }
                .card { padding: 20px; }
                .registry-table { font-size: 0.9rem; }
                .registry-table th, .registry-table td { padding: 8px; }
            }
        </style>
    </head>
    <body>
        <div class="container">
            <header class="header">
                <h1>🚀 Zot 公益镜像仓库</h1>
                <p class="subtitle">🌟 高速、稳定、免费的容器镜像加速服务</p>
                <p class="description">专为中国大陆开发者提供的容器镜像加速服务，基于 Zot 构建，支持 Docker Hub、gcr.io、registry.k8s.io、quay.io 等主流镜像源的自动同步与缓存。</p>
            </header>
            <div class="card">
                <h2 class="section-title">🌐 服务地址</h2>
                <div class="service-info">
                    <div class="info-item"><strong>镜像仓库：</strong> docker.at9.net</div>
                    <div class="info-item"><strong>Web UI：</strong> <a href="https://docker.at9.net/home" target="_blank">https://docker.at9.net/home</a> (点击进入仓库浏览)</div>
                </div>
            </div>
            <div class="card">
                <h2 class="section-title">📋 支持的镜像源</h2>
                <table class="registry-table">
                    <thead><tr><th>原始仓库</th><th>访问方式</th><th>说明</th></tr></thead>
                    <tbody>
                        <tr><td>Docker Hub</td><td><code>docker.at9.net/library/{镜像名}</code></td><td>Docker 官方镜像库</td></tr>
                        <tr><td>gcr.io</td><td><code>docker.at9.net/{gcr.io前缀}</code></td><td>Google Container Registry</td></tr>
                        <tr><td>registry.k8s.io</td><td><code>docker.at9.net/{k8s镜像名}</code></td><td>Kubernetes 官方镜像仓库</td></tr>
                        <tr><td>k8s.gcr.io</td><td><code>docker.at9.net/{k8s镜像名}</code></td><td>Kubernetes 镜像 (兼容重定向)</td></tr>
                        <tr><td>quay.io</td><td><code>docker.at9.net/{quay.io前缀}</code></td><td>Red Hat Quay 镜像仓库</td></tr>
                        <tr><td>ghcr.io</td><td><code>docker.at9.net/{ghcr.io前缀}</code></td><td>GitHub Container Registry</td></tr>
                        <tr><td>mcr.microsoft.com</td><td><code>docker.at9.net/{mcr前缀}</code></td><td>Microsoft Container Registry</td></tr>
                    </tbody>
                </table>
            </div>
            <div class="card">
                <div class="usage-section">
                    <h3 class="usage-title">🚀 使用方法</h3>
                    <div class="usage-simple">
                        <h4>🎯 超简单使用 - 只需加前缀！</h4>
                        <p>原理：在任何镜像地址前加上 <strong>docker.at9.net/</strong>，系统自动识别来源并加速下载！</p>
                        <p># 基本语法：在原始镜像地址前加前缀 docker.at9.net/</p>
                        <code>docker pull docker.at9.net/{原始完整镜像地址}</code>
                    </div>
                    <h4>📦 实用案例：</h4>
                    <div class="examples">
                        <div class="example-box">
                            <strong>Docker Hub 官方镜像（已验证）：</strong>
                            <code>docker pull docker.at9.net/nginx:latest</code>
                            <code>docker pull docker.at9.net/node:18-alpine</code>
                            <code>docker pull docker.at9.net/mysql:8.0</code>
                            <code>docker pull docker.at9.net/redis:7-alpine</code>
                            <code>docker pull docker.at9.net/tomcat:latest</code>
                        </div>
                        <div class="example-box">
                            <strong>Microsoft Container Registry（已验证）：</strong>
                            <code>docker pull docker.at9.net/dotnet/runtime:8.0</code>
                            <code>docker pull docker.at9.net/dotnet/sdk:8.0</code>
                        </div>
                    </div>
                    <div class="examples">
                        <div class="example-box">
                            <strong>Kubernetes 官方镜像（已验证）：</strong>
                            <code>docker pull docker.at9.net/pause:3.9</code>
                            <code>docker pull docker.at9.net/coredns/coredns:v1.10.1</code>
                        </div>
                        <div class="example-box">
                            <strong>Google Container Registry（已验证）：</strong>
                            <code>docker pull docker.at9.net/distroless/static-debian11:latest</code>
                        </div>
                    </div>
                    <div class="examples">
                        <div class="example-box">
                            <strong>Quay.io 企业级镜像（已验证）：</strong>
                            <code>docker pull docker.at9.net/prometheus/prometheus:latest</code>
                        </div>
                                                 <div class="example-box">
                             <strong>Kubernetes YAML 中使用（已验证）：</strong>
                             <code>image: docker.at9.net/nginx:latest</code>
                             <code>image: docker.at9.net/redis:7-alpine</code>
                             <code>image: docker.at9.net/pause:3.9</code>
                         </div>
                     </div>
                     <div class="examples">
                         <div class="example-box">
                             <strong>使用提示：</strong>
                             <code># 建议先访问 Web 界面验证镜像可用性</code>
                             <code># https://docker.at9.net</code>
                             <code># 某些镜像可能需要认证或特定版本</code>
                         </div>
                         <div class="example-box">
                             <strong>验证镜像命令：</strong>
                             <code>curl -I https://docker.at9.net/v2/nginx/manifests/latest</code>
                             <code># 返回 200 状态码表示镜像可用</code>
                         </div>
                     </div>
                </div>
            </div>
            <footer class="footer">
                <p>Powered by <strong>Zot</strong> | 为开源社区提供免费服务</p>
                <p style="margin-top: 10px;">
                    <a href="https://github.com/htazq/container-mirror-for-china" target="_blank" style="color: white; text-decoration: none; opacity: 0.8; font-size: 0.9rem;">
                        📂 GitHub 项目地址
                    </a>
                    <span style="margin: 0 10px; opacity: 0.6;">|</span>
                    <a href="https://github.com/htazq/container-mirror-for-china/issues" target="_blank" style="color: white; text-decoration: none; opacity: 0.8; font-size: 0.9rem;">
                        🐛 问题反馈
                    </a>
                </p>
            </footer>
        </div>
    </body>
    </html> 