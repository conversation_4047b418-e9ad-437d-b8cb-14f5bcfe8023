#!/bin/bash

echo "🔧 正在修复 nginx 路径重写配置..."

# 应用修复后的配置
echo "📋 应用新的 nginx 配置..."
kubectl apply -f nginx-config-fixed-simple.yaml

# 重启 nginx pod 使配置生效
echo "🔄 重启 nginx pod..."
kubectl rollout restart deployment/nginx-proxy -n zot

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/nginx-proxy -n zot --timeout=120s

echo "✅ nginx 配置修复完成！"
echo ""
echo "🧪 现在可以测试以下完整路径："
echo "  docker pull docker.at9.net/docker.io/nginx:latest"
echo "  docker pull docker.at9.net/registry.k8s.io/pause:3.9"
echo "  docker pull docker.at9.net/k8s.gcr.io/pause:3.6"
echo "  docker pull docker.at9.net/quay.io/prometheus/prometheus:latest"
echo "  docker pull docker.at9.net/ghcr.io/user/repo:tag"
echo "  docker pull docker.at9.net/mcr.microsoft.com/dotnet/runtime:8.0"
echo ""
echo "📝 关键修复点："
echo "  ✅ 添加了正确的 /v2/ 前缀匹配"
echo "  ✅ 修复了 rewrite 规则的目标路径"
echo "  ✅ 保持了 nginx events 和 http 块的完整结构"
echo "  ✅ 简化了配置，提高了稳定性" 