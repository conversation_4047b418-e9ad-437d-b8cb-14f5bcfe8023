USER-SUPPLIED VALUES:
additionalPrometheusRulesMap: {}
alertmanager:
  alertmanagerSpec:
    additionalConfig: {}
    additionalConfigString: ""
    additionalPeers: []
    affinity: {}
    alertmanagerConfigMatcherStrategy: {}
    alertmanagerConfigNamespaceSelector: {}
    alertmanagerConfigSelector: {}
    alertmanagerConfiguration: {}
    storage:
      volumeClaimTemplate:
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 2Gi
          storageClassName: nfs-client
    stringConfig: |
      global:
        resolve_timeout: 5m
      inhibit_rules:
      - equal:
        - namespace
        - alertname
        source_matchers:
        - severity = critical
        target_matchers:
        - severity =~ warning|info
      - equal:
        - namespace
        - alertname
        source_matchers:
        - severity = warning
        target_matchers:
        - severity = info
      - equal:
        - namespace
        source_matchers:
        - alertname = InfoInhibitor
        target_matchers:
        - severity = info
      - target_matchers:
        - alertname = InfoInhibitor
      receivers:
      - name: "null"
      - name: wxpusher
        webhook_configs:
        - body: |
            {
              "appToken": "AT_UB1R4J5Q5VBz7kSxE886mne6IzzrlgdF",
              "content": "{{ .CommonAnnotations.description | toUpper }}",
              "summary": "{{ .CommonLabels.alertname }}",
              "contentType": 2,
              "uids": [
                "UID_RorFljOBGQzcxnRe36vtoWek4F13"
              ],
              "verifyPayType": 0
            }
          http_config: {}
          send_resolved: true
          url: https://wxpusher.zjiecode.com/api/send/message
      route:
        group_by:
        - namespace
        group_interval: 5m
        group_wait: 30s
        receiver: wxpusher
        repeat_interval: 12h
        routes:
        - matchers:
          - alertname = "Watchdog"
          receiver: "null"
      templates:
      - /etc/alertmanager/config/*.tmpl
    automountServiceAccountToken: true
    clusterAdvertiseAddress: false
    clusterGossipInterval: ""
    clusterLabel: ""
    clusterPeerTimeout: ""
    clusterPushpullInterval: ""
    configMaps: []
    containers: []
    enabled: true
    externalUrl: null
    forceEnableClusterMode: True
    image:
      registry: quay.io
      repository: prometheus/alertmanager
      sha: ""
      tag: v0.28.1
    initContainers: []
    listenLocal: false
    logFormat: logfmt
    logLevel: info
    minReadySeconds: 0
    nodeSelector: {}
    paused: false
    persistentVolumeClaimRetentionPolicy: {}
    podAntiAffinity: soft
    podAntiAffinityTopologyKey: kubernetes.io/hostname
    podMetadata: {}
    portName: http-web
    priorityClassName: ""
    replicas: 1
    resources: {}
    retention: 120h
    routePrefix: /
    scheme: ""
    secrets: []
    securityContext:
      fsGroup: 2000
      runAsGroup: 2000
      runAsNonRoot: true
      runAsUser: 1000
      seccompProfile:
        type: RuntimeDefault
    serviceName: null
    tlsConfig: {}
    tolerations: []
    topologySpreadConstraints: []
    useExistingSecret: false
    volumeMounts: []
    volumes: []
    web: {}
  annotations: {}
  apiVersion: v2
  enableFeatures: []
  enabled: true
  extraSecret:
    annotations: {}
    data: {}
  forceDeployDashboards: false
  ingress:
    annotations: {}
    enabled: false
    hosts: []
    labels: {}
    paths: []
    tls: []
  ingressPerReplica:
    annotations: {}
    enabled: false
    hostDomain: ""
    hostPrefix: ""
    labels: {}
    paths: []
    tlsSecretName: ""
    tlsSecretPerReplica:
      enabled: false
      prefix: alertmanager
  networkPolicy:
    additionalIngress: []
    egress:
      enabled: false
      rules: []
    enableClusterRules: true
    enabled: false
    gateway:
      namespace: ""
      podLabels: {}
    monitoringRules:
      configReloader: true
      prometheus: true
    policyTypes:
    - Ingress
  podDisruptionBudget:
    enabled: false
    maxUnavailable: ""
    minAvailable: 1
  route:
    main:
      additionalRules: []
      annotations: {}
      apiVersion: gateway.networking.k8s.io/v1
      enabled: false
      filters: []
      hostnames: []
      httpsRedirect: false
      kind: HTTPRoute
      labels: {}
      matches:
      - path:
          type: PathPrefix
          value: /
      parentRefs: []
  secret:
    annotations: {}
  service:
    additionalPorts: []
    annotations: {}
    clusterIP: ""
    enabled: true
    externalIPs: []
    externalTrafficPolicy: Cluster
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels: {}
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    nodePort: 30903
    port: 9093
    sessionAffinity: None
    sessionAffinityConfig:
      clientIP:
        timeoutSeconds: 10800
    targetPort: 9093
    type: ClusterIP
  serviceAccount:
    annotations: {}
    automountServiceAccountToken: true
    create: true
    name: ""
  serviceMonitor:
    additionalEndpoints: []
    additionalLabels: {}
    bearerTokenFile: null
    enableHttp2: true
    interval: ""
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    scheme: ""
    selfMonitor: true
    targetLimit: 0
    tlsConfig: {}
  servicePerReplica:
    annotations: {}
    enabled: false
    externalTrafficPolicy: Cluster
    loadBalancerSourceRanges: []
    nodePort: 30904
    port: 9093
    targetPort: 9093
    type: ClusterIP
  templateFiles: {}
  tplConfig: false
cleanPrometheusOperatorObjectNames: false
commonLabels: {}
coreDns:
  enabled: true
  service:
    enabled: true
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    port: 9153
    targetPort: 9153
  serviceMonitor:
    additionalLabels: {}
    enabled: true
    interval: ""
    jobLabel: jobLabel
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    port: http-metrics
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector: {}
    targetLabels: []
    targetLimit: 0
crds:
  enabled: true
  upgradeJob:
    affinity: {}
    annotations: {}
    containerSecurityContext:
      allowPrivilegeEscalation: false
      capabilities:
        drop:
        - ALL
      readOnlyRootFilesystem: true
    enabled: false
    env: {}
    extraVolumeMounts: []
    extraVolumes: []
    forceConflicts: false
    image:
      busybox:
        pullPolicy: IfNotPresent
        registry: docker.io
        repository: busybox
        sha: ""
        tag: latest
      kubectl:
        pullPolicy: IfNotPresent
        registry: registry.k8s.io
        repository: kubectl
        sha: ""
        tag: ""
    labels: {}
    nodeSelector: {}
    podAnnotations: {}
    podLabels: {}
    podSecurityContext:
      fsGroup: 65534
      runAsGroup: 65534
      runAsNonRoot: true
      runAsUser: 65534
      seccompProfile:
        type: RuntimeDefault
    resources: {}
    serviceAccount:
      annotations: {}
      automountServiceAccountToken: true
      create: true
      labels: {}
      name: ""
    tolerations: []
    topologySpreadConstraints: []
customRules: {}
defaultRules:
  additionalAggregationLabels: []
  additionalRuleAnnotations: {}
  additionalRuleGroupAnnotations:
    alertmanager: {}
    configReloaders: {}
    etcd: {}
    general: {}
    k8sContainerCpuUsageSecondsTotal: {}
    k8sContainerMemoryCache: {}
    k8sContainerMemoryRss: {}
    k8sContainerMemorySwap: {}
    k8sContainerResource: {}
    k8sPodOwner: {}
    kubeApiserverAvailability: {}
    kubeApiserverBurnrate: {}
    kubeApiserverHistogram: {}
    kubeApiserverSlos: {}
    kubeControllerManager: {}
    kubePrometheusGeneral: {}
    kubePrometheusNodeRecording: {}
    kubeProxy: {}
    kubeSchedulerAlerting: {}
    kubeSchedulerRecording: {}
    kubeStateMetrics: {}
    kubelet: {}
    kubernetesApps: {}
    kubernetesResources: {}
    kubernetesStorage: {}
    kubernetesSystem: {}
    network: {}
    node: {}
    nodeExporterAlerting: {}
    nodeExporterRecording: {}
    prometheus: {}
    prometheusOperator: {}
  additionalRuleGroupLabels:
    alertmanager: {}
    configReloaders: {}
    etcd: {}
    general: {}
    k8sContainerCpuUsageSecondsTotal: {}
    k8sContainerMemoryCache: {}
    k8sContainerMemoryRss: {}
    k8sContainerMemorySwap: {}
    k8sContainerResource: {}
    k8sPodOwner: {}
    kubeApiserverAvailability: {}
    kubeApiserverBurnrate: {}
    kubeApiserverHistogram: {}
    kubeApiserverSlos: {}
    kubeControllerManager: {}
    kubePrometheusGeneral: {}
    kubePrometheusNodeRecording: {}
    kubeProxy: {}
    kubeSchedulerAlerting: {}
    kubeSchedulerRecording: {}
    kubeStateMetrics: {}
    kubelet: {}
    kubernetesApps: {}
    kubernetesResources: {}
    kubernetesStorage: {}
    kubernetesSystem: {}
    network: {}
    node: {}
    nodeExporterAlerting: {}
    nodeExporterRecording: {}
    prometheus: {}
    prometheusOperator: {}
  additionalRuleLabels: {}
  annotations: {}
  appNamespacesTarget: .*
  create: true
  disabled: {}
  keepFiringFor: ""
  labels: {}
  node:
    fsSelector: fstype!=""
  rules:
    alertmanager: true
    configReloaders: true
    etcd: true
    general: true
    k8sContainerCpuUsageSecondsTotal: true
    k8sContainerMemoryCache: true
    k8sContainerMemoryRss: true
    k8sContainerMemorySwap: true
    k8sContainerMemoryWorkingSetBytes: true
    k8sContainerResource: true
    k8sPodOwner: true
    kubeApiserverAvailability: true
    kubeApiserverBurnrate: true
    kubeApiserverHistogram: true
    kubeApiserverSlos: true
    kubeControllerManager: true
    kubePrometheusGeneral: true
    kubePrometheusNodeRecording: true
    kubeProxy: true
    kubeSchedulerAlerting: true
    kubeSchedulerRecording: true
    kubeStateMetrics: true
    kubelet: true
    kubernetesApps: true
    kubernetesResources: true
    kubernetesStorage: true
    kubernetesSystem: true
    network: true
    node: true
    nodeExporterAlerting: true
    nodeExporterRecording: true
    prometheus: true
    prometheusOperator: true
    windows: true
  runbookUrl: https://runbooks.prometheus-operator.dev/runbooks
extraManifests: []
fullnameOverride: ""
global:
  imagePullSecrets: []
  imageRegistry: ""
  rbac:
    create: true
    createAggregateClusterRoles: false
    pspAnnotations: {}
    pspEnabled: false
grafana:
  additionalDataSources: []
  adminPassword: hentian
  adminUser: admin
  defaultDashboardsEditable: true
  defaultDashboardsEnabled: true
  defaultDashboardsInterval: 1m
  defaultDashboardsTimezone: utc
  deleteDatasources: []
  enabled: true
  extraConfigmapMounts: []
  forceDeployDashboards: false
  forceDeployDatasources: false
  grafana.ini:
    date_formats:
      default_timezone: Asia/Shanghai
  ingress:
    annotations: {}
    enabled: false
    hosts: []
    labels: {}
    path: /
    tls: []
  namespaceOverride: ""
  persistence:
    accessModes:
    - ReadWriteOnce
    enabled: true
    finalizers:
    - kubernetes.io/pvc-protection
    size: 5Gi
    storageClassName: nfs-client
    type: pvc
  prune: false
  rbac:
    pspEnabled: false
  service:
    ipFamilies: []
    ipFamilyPolicy: ""
    portName: http-web
  serviceAccount:
    autoMount: true
    create: true
  serviceMonitor:
    enabled: true
    interval: ""
    labels: {}
    path: /metrics
    relabelings: []
    scheme: http
    scrapeTimeout: 30s
    tlsConfig: {}
  sidecar:
    dashboards:
      annotations: {}
      enableNewTablePanelSyntax: false
      enabled: true
      label: grafana_dashboard
      labelValue: "1"
      multicluster:
        etcd:
          enabled: false
        global:
          enabled: false
      provider:
        allowUiUpdates: false
      searchNamespace: ALL
    datasources:
      alertmanager:
        enabled: true
        handleGrafanaManagedAlerts: false
        implementation: prometheus
        name: Alertmanager
        uid: alertmanager
      annotations: {}
      createPrometheusReplicasDatasources: false
      defaultDatasourceEnabled: true
      enabled: true
      exemplarTraceIdDestinations: {}
      httpMethod: POST
      isDefaultDatasource: true
      label: grafana_datasource
      labelValue: "1"
      name: Prometheus
      prometheusServiceName: prometheus-operated
      uid: prometheus
kube-state-metrics:
  namespaceOverride: ""
  prometheus:
    monitor:
      enabled: true
      honorLabels: true
      interval: ""
      labelLimit: 0
      labelNameLengthLimit: 0
      labelValueLengthLimit: 0
      metricRelabelings: []
      proxyUrl: ""
      relabelings: []
      sampleLimit: 0
      scrapeTimeout: ""
      targetLimit: 0
  prometheusScrape: false
  rbac:
    create: true
  releaseLabel: true
  selfMonitor:
    enabled: false
kubeApiServer:
  enabled: true
  serviceMonitor:
    additionalLabels: {}
    enabled: true
    interval: ""
    jobLabel: component
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings:
    - action: drop
      regex: (etcd_request|apiserver_request_slo|apiserver_request_sli|apiserver_request)_duration_seconds_bucket;(0\.15|0\.2|0\.3|0\.35|0\.4|0\.45|0\.6|0\.7|0\.8|0\.9|1\.25|1\.5|1\.75|2|3|3\.5|4|4\.5|6|7|8|9|15|20|30|40|45|50)(\.0)?
      sourceLabels:
      - __name__
      - le
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector:
      matchLabels:
        component: apiserver
        provider: kubernetes
    targetLabels: []
    targetLimit: 0
  tlsConfig:
    insecureSkipVerify: false
    serverName: kubernetes
kubeControllerManager:
  enabled: true
  endpoints: []
  service:
    enabled: true
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    port: null
    targetPort: null
  serviceMonitor:
    additionalLabels: {}
    enabled: true
    https: null
    insecureSkipVerify: null
    interval: ""
    jobLabel: jobLabel
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    port: http-metrics
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector: {}
    serverName: null
    targetLabels: []
    targetLimit: 0
kubeDns:
  enabled: false
  service:
    dnsmasq:
      port: 10054
      targetPort: 10054
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    skydns:
      port: 10055
      targetPort: 10055
  serviceMonitor:
    additionalLabels: {}
    dnsmasqMetricRelabelings: []
    dnsmasqRelabelings: []
    interval: ""
    jobLabel: jobLabel
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector: {}
    targetLabels: []
    targetLimit: 0
kubeEtcd:
  enabled: true
  endpoints: []
  service:
    enabled: true
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    port: 2381
    targetPort: 2381
  serviceMonitor:
    additionalLabels: {}
    caFile: ""
    certFile: ""
    enabled: true
    insecureSkipVerify: false
    interval: ""
    jobLabel: jobLabel
    keyFile: ""
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    port: http-metrics
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    scheme: http
    selector: {}
    serverName: ""
    targetLabels: []
    targetLimit: 0
kubeProxy:
  enabled: true
  endpoints: []
  service:
    enabled: true
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    port: 10249
    targetPort: 10249
  serviceMonitor:
    additionalLabels: {}
    enabled: true
    https: false
    interval: ""
    jobLabel: jobLabel
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    port: http-metrics
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector: {}
    targetLabels: []
    targetLimit: 0
kubeScheduler:
  enabled: true
  endpoints: []
  service:
    enabled: true
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    port: null
    targetPort: null
  serviceMonitor:
    additionalLabels: {}
    enabled: true
    https: null
    insecureSkipVerify: null
    interval: ""
    jobLabel: jobLabel
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    port: http-metrics
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    selector: {}
    serverName: null
    targetLabels: []
    targetLimit: 0
kubeStateMetrics:
  enabled: true
kubeTargetVersionOverride: ""
kubeVersionOverride: ""
kubelet:
  enabled: true
  namespace: kube-system
  serviceMonitor:
    additionalLabels: {}
    attachMetadata:
      node: false
    cAdvisor: true
    cAdvisorInterval: 10s
    cAdvisorMetricRelabelings:
    - action: drop
      regex: container_cpu_(cfs_throttled_seconds_total|load_average_10s|system_seconds_total|user_seconds_total)
      sourceLabels:
      - __name__
    - action: drop
      regex: container_fs_(io_current|io_time_seconds_total|io_time_weighted_seconds_total|reads_merged_total|sector_reads_total|sector_writes_total|writes_merged_total)
      sourceLabels:
      - __name__
    - action: drop
      regex: container_memory_(mapped_file|swap)
      sourceLabels:
      - __name__
    - action: drop
      regex: container_(file_descriptors|tasks_state|threads_max)
      sourceLabels:
      - __name__
    - action: drop
      regex: container_memory_failures_total;hierarchy
      sourceLabels:
      - __name__
      - scope
    - action: drop
      regex: container_network_.*;(cali|cilium|cni|lxc|nodelocaldns|tunl).*
      sourceLabels:
      - __name__
      - interface
    - action: drop
      regex: container_spec.*
      sourceLabels:
      - __name__
    - action: drop
      regex: .+;
      sourceLabels:
      - id
      - pod
    cAdvisorRelabelings:
    - action: replace
      sourceLabels:
      - __metrics_path__
      targetLabel: metrics_path
    enabled: true
    honorLabels: true
    honorTimestamps: true
    https: true
    insecureSkipVerify: true
    interval: ""
    kubelet: true
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings:
    - action: drop
      regex: (csi_operations|storage_operation_duration)_seconds_bucket;(0.25|2.5|15|25|120|600)(\.0)?
      sourceLabels:
      - __name__
      - le
    probes: true
    probesMetricRelabelings: []
    probesRelabelings:
    - action: replace
      sourceLabels:
      - __metrics_path__
      targetLabel: metrics_path
    proxyUrl: ""
    relabelings:
    - action: replace
      sourceLabels:
      - __metrics_path__
      targetLabel: metrics_path
    resource: false
    resourceInterval: 10s
    resourcePath: /metrics/resource/v1alpha1
    resourceRelabelings:
    - action: replace
      sourceLabels:
      - __metrics_path__
      targetLabel: metrics_path
    sampleLimit: 0
    targetLabels: []
    targetLimit: 0
    trackTimestampsStaleness: true
kubernetesServiceMonitors:
  enabled: true
nameOverride: ""
namespaceOverride: ""
nodeExporter:
  enabled: true
  forceDeployDashboards: false
  operatingSystems:
    aix:
      enabled: true
    darwin:
      enabled: true
    linux:
      enabled: true
prometheus:
  additionalPodMonitors: []
  additionalRulesForClusterRole: []
  additionalServiceMonitors: []
  agentMode: false
  annotations: {}
  enabled: true
  extraSecret:
    annotations: {}
    data: {}
  ingress:
    annotations: {}
    enabled: false
    hosts: []
    labels: {}
    paths: []
    tls: []
  ingressPerReplica:
    annotations: {}
    enabled: false
    hostDomain: ""
    hostPrefix: ""
    labels: {}
    paths: []
    tlsSecretName: ""
    tlsSecretPerReplica:
      enabled: false
      prefix: prometheus
  networkPolicy:
    enabled: false
    flavor: kubernetes
  podDisruptionBudget:
    enabled: false
    maxUnavailable: ""
    minAvailable: 1
  podSecurityPolicy:
    allowedCapabilities: []
    allowedHostPaths: []
    volumes: []
  prometheusSpec:
    additionalAlertManagerConfigs: []
    additionalAlertManagerConfigsSecret: {}
    additionalAlertRelabelConfigs: []
    additionalAlertRelabelConfigsSecret: {}
    additionalArgs: []
    additionalConfig: {}
    additionalConfigString: ""
    additionalPrometheusSecretsAnnotations: {}
    additionalRemoteRead: []
    additionalRemoteWrite: []
    additionalScrapeConfigs: []
    additionalScrapeConfigsSecret: {}
    affinity: {}
    alertingEndpoints: []
    allowOverlappingBlocks: false
    apiserverConfig: {}
    arbitraryFSAccessThroughSMs: false
    automountServiceAccountToken: true
    configMaps: []
    containers: []
    disableCompaction: false
    enableAdminAPI: false
    enableFeatures: []
    enableRemoteWriteReceiver: false
    enforcedKeepDroppedTargets: 0
    enforcedLabelLimit: false
    enforcedLabelNameLengthLimit: false
    enforcedLabelValueLengthLimit: false
    enforcedNamespaceLabel: ""
    enforcedSampleLimit: false
    enforcedTargetLimit: false
    evaluationInterval: ""
    excludedFromEnforcement: []
    exemplars: {}
    externalLabels: {}
    externalUrl: ""
    hostAliases: []
    hostNetwork: false
    ignoreNamespaceSelectors: false
    image:
      registry: quay.io
      repository: prometheus/prometheus
      sha: ""
      tag: v3.2.1
    initContainers: []
    listenLocal: false
    logFormat: logfmt
    logLevel: info
    maximumStartupDurationSeconds: 0
    minReadySeconds: 0
    nameValidationScheme: ""
    nodeSelector: {}
    overrideHonorLabels: false
    overrideHonorTimestamps: false
    paused: false
    persistentVolumeClaimRetentionPolicy: {}
    podAntiAffinity: soft
    podAntiAffinityTopologyKey: kubernetes.io/hostname
    podMetadata: {}
    podMonitorNamespaceSelector: {}
    podMonitorSelector: {}
    podMonitorSelectorNilUsesHelmValues: true
    portName: http-web
    priorityClassName: ""
    probeNamespaceSelector: {}
    probeSelector: {}
    probeSelectorNilUsesHelmValues: true
    prometheusExternalLabelName: ""
    prometheusExternalLabelNameClear: false
    prometheusRulesExcludedFromEnforce: []
    query: {}
    queryLogFile: false
    remoteRead: []
    remoteWrite: []
    remoteWriteDashboards: false
    replicaExternalLabelName: ""
    replicaExternalLabelNameClear: false
    replicas: 1
    resources: {}
    retention: 5d
    retentionSize: ""
    routePrefix: /
    ruleNamespaceSelector: {}
    ruleSelector: {}
    ruleSelectorNilUsesHelmValues: true
    sampleLimit: false
    scrapeClasses: []
    scrapeConfigNamespaceSelector: {}
    scrapeConfigSelector: {}
    scrapeConfigSelectorNilUsesHelmValues: true
    scrapeFailureLogFile: ""
    scrapeInterval: ""
    scrapeTimeout: ""
    secrets: []
    securityContext:
      fsGroup: 2000
      runAsGroup: 2000
      runAsNonRoot: true
      runAsUser: 1000
      seccompProfile:
        type: RuntimeDefault
    serviceDiscoveryRole: ""
    serviceMonitorNamespaceSelector: {}
    serviceMonitorSelector: {}
    serviceMonitorSelectorNilUsesHelmValues: true
    serviceName: null
    shards: 1
    storage:
      volumeClaimTemplate:
        spec:
          accessModes:
          - ReadWriteOnce
          resources:
            requests:
              storage: 10Gi
          storageClassName: local-path
    storageSpec: {}
    thanos: {}
    tolerations: []
    topologySpreadConstraints: []
    tracingConfig: {}
    tsdb:
      outOfOrderTimeWindow: 0s
    version: ""
    volumeMounts: []
    volumes: []
    walCompression: true
    web: {}
  route:
    main:
      additionalRules: []
      annotations: {}
      apiVersion: gateway.networking.k8s.io/v1
      enabled: false
      filters: []
      hostnames: []
      httpsRedirect: false
      kind: HTTPRoute
      labels: {}
      matches:
      - path:
          type: PathPrefix
          value: /
      parentRefs: []
  service:
    additionalPorts: []
    annotations: {}
    clusterIP: ""
    enabled: true
    externalIPs: []
    externalTrafficPolicy: Cluster
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels: {}
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    nodePort: 30090
    port: 9090
    publishNotReadyAddresses: false
    reloaderWebPort: 8080
    sessionAffinity: None
    sessionAffinityConfig:
      clientIP:
        timeoutSeconds: 10800
    targetPort: 9090
    type: ClusterIP
  serviceAccount:
    annotations: {}
    automountServiceAccountToken: true
    create: true
    name: ""
  serviceMonitor:
    additionalEndpoints: []
    additionalLabels: {}
    bearerTokenFile: null
    interval: ""
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    relabelings: []
    sampleLimit: 0
    scheme: ""
    selfMonitor: true
    targetLimit: 0
    tlsConfig: {}
  servicePerReplica:
    annotations: {}
    enabled: false
    externalTrafficPolicy: Cluster
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    loadBalancerSourceRanges: []
    nodePort: 30091
    port: 9090
    targetPort: 9090
    type: ClusterIP
  thanosIngress:
    annotations: {}
    enabled: false
    hosts: []
    labels: {}
    nodePort: 30901
    paths: []
    servicePort: 10901
    tls: []
  thanosService:
    annotations: {}
    clusterIP: None
    enabled: false
    externalTrafficPolicy: Cluster
    httpNodePort: 30902
    httpPort: 10902
    httpPortName: http
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels: {}
    nodePort: 30901
    port: 10901
    portName: grpc
    targetHttpPort: http
    targetPort: grpc
    type: ClusterIP
  thanosServiceExternal:
    annotations: {}
    enabled: false
    externalTrafficPolicy: Cluster
    httpNodePort: 30902
    httpPort: 10902
    httpPortName: http
    labels: {}
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    nodePort: 30901
    port: 10901
    portName: grpc
    targetHttpPort: http
    targetPort: grpc
    type: LoadBalancer
  thanosServiceMonitor:
    additionalLabels: {}
    bearerTokenFile: null
    enabled: false
    interval: ""
    metricRelabelings: []
    relabelings: []
    scheme: ""
    tlsConfig: {}
prometheus-node-exporter:
  extraArgs:
  - --collector.filesystem.mount-points-exclude=^/(dev|proc|sys|var/lib/docker/.+|var/lib/kubelet/.+)($|/)
  - --collector.filesystem.fs-types-exclude=^(autofs|binfmt_misc|bpf|cgroup2?|configfs|debugfs|devpts|devtmpfs|fusectl|hugetlbfs|iso9660|mqueue|nsfs|overlay|proc|procfs|pstore|rpc_pipefs|securityfs|selinuxfs|squashfs|sysfs|tracefs)$
  hostNetwork: false
  namespaceOverride: ""
  podLabels:
    jobLabel: node-exporter
  prometheus:
    monitor:
      enabled: true
      interval: ""
      jobLabel: jobLabel
      labelLimit: 0
      labelNameLengthLimit: 0
      labelValueLengthLimit: 0
      metricRelabelings: []
      proxyUrl: ""
      relabelings: []
      sampleLimit: 0
      scrapeTimeout: ""
      targetLimit: 0
  rbac:
    pspEnabled: false
  releaseLabel: true
  service:
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels:
      jobLabel: node-exporter
    port: 9100
    portName: http-metrics
    targetPort: 9100
    type: ClusterIP
prometheus-windows-exporter:
  config: |-
    collectors:
      enabled: '[defaults],memory,container'
  podLabels:
    jobLabel: windows-exporter
  prometheus:
    monitor:
      enabled: true
      jobLabel: jobLabel
  releaseLabel: true
prometheusOperator:
  admissionWebhooks:
    annotations: {}
    caBundle: ""
    certManager:
      admissionCert:
        duration: ""
        revisionHistoryLimit: null
      enabled: false
      rootCert:
        duration: ""
        revisionHistoryLimit: null
    createSecretJob:
      securityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop:
          - ALL
        readOnlyRootFilesystem: true
    deployment:
      affinity: {}
      annotations: {}
      automountServiceAccountToken: true
      containerSecurityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop:
          - ALL
        readOnlyRootFilesystem: true
      dnsConfig: {}
      enabled: false
      hostNetwork: false
      image:
        pullPolicy: IfNotPresent
        registry: quay.io
        repository: prometheus-operator/admission-webhook
        sha: ""
        tag: ""
      labels: {}
      livenessProbe:
        enabled: true
        failureThreshold: 3
        initialDelaySeconds: 30
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      nodeSelector: {}
      podAnnotations: {}
      podDisruptionBudget: {}
      podLabels: {}
      readinessProbe:
        enabled: true
        failureThreshold: 3
        initialDelaySeconds: 5
        periodSeconds: 10
        successThreshold: 1
        timeoutSeconds: 1
      replicas: 1
      resources: {}
      revisionHistoryLimit: 10
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        runAsNonRoot: true
        runAsUser: 65534
        seccompProfile:
          type: RuntimeDefault
      service:
        additionalPorts: []
        annotations: {}
        clusterIP: ""
        externalIPs: []
        externalTrafficPolicy: Cluster
        ipDualStack:
          enabled: false
          ipFamilies:
          - IPv6
          - IPv4
          ipFamilyPolicy: PreferDualStack
        labels: {}
        loadBalancerIP: ""
        loadBalancerSourceRanges: []
        nodePort: 31080
        nodePortTls: 31443
        type: ClusterIP
      serviceAccount:
        annotations: {}
        automountServiceAccountToken: false
        create: true
        name: ""
      strategy: {}
      tls:
        enabled: true
        internalPort: 10250
        tlsMinVersion: VersionTLS13
      tolerations: []
    enabled: true
    failurePolicy: ""
    mutatingWebhookConfiguration:
      annotations: {}
    namespaceSelector: {}
    objectSelector: {}
    patch:
      affinity: {}
      annotations: {}
      enabled: true
      image:
        pullPolicy: IfNotPresent
        registry: registry.k8s.io
        repository: ingress-nginx/kube-webhook-certgen
        sha: ""
        tag: v1.5.1
      nodeSelector: {}
      podAnnotations: {}
      priorityClassName: ""
      resources: {}
      securityContext:
        runAsGroup: 2000
        runAsNonRoot: true
        runAsUser: 2000
        seccompProfile:
          type: RuntimeDefault
      serviceAccount:
        annotations: {}
        automountServiceAccountToken: true
        create: true
      tolerations: []
      ttlSecondsAfterFinished: 60
    patchWebhookJob:
      securityContext:
        allowPrivilegeEscalation: false
        capabilities:
          drop:
          - ALL
        readOnlyRootFilesystem: true
    timeoutSeconds: 10
    validatingWebhookConfiguration:
      annotations: {}
  affinity: {}
  alertmanagerConfigNamespaces: []
  alertmanagerInstanceNamespaces: []
  alertmanagerInstanceSelector: ""
  annotations: {}
  automountServiceAccountToken: true
  containerSecurityContext:
    allowPrivilegeEscalation: false
    capabilities:
      drop:
      - ALL
    readOnlyRootFilesystem: true
  denyNamespaces: []
  dnsConfig: {}
  enabled: true
  env:
    GOGC: "30"
  extraArgs: []
  extraVolumeMounts: []
  extraVolumes: []
  fullnameOverride: ""
  hostNetwork: false
  image:
    pullPolicy: IfNotPresent
    registry: quay.io
    repository: prometheus-operator/prometheus-operator
    sha: ""
    tag: ""
  kubeletEndpointSliceEnabled: false
  kubeletEndpointsEnabled: true
  kubeletService:
    enabled: true
    name: ""
    namespace: kube-system
    selector: ""
  labels: {}
  lifecycle: {}
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 0
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  namespaces: {}
  networkPolicy:
    enabled: false
    flavor: kubernetes
  nodeSelector: {}
  podAnnotations: {}
  podLabels: {}
  prometheusConfigReloader:
    enableProbe: false
    image:
      registry: quay.io
      repository: prometheus-operator/prometheus-config-reloader
      sha: ""
      tag: ""
    resources: {}
  prometheusInstanceNamespaces: []
  prometheusInstanceSelector: ""
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 0
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  resources: {}
  revisionHistoryLimit: 10
  secretFieldSelector: type!=kubernetes.io/dockercfg,type!=kubernetes.io/service-account-token,type!=helm.sh/release.v1
  securityContext:
    fsGroup: 65534
    runAsGroup: 65534
    runAsNonRoot: true
    runAsUser: 65534
    seccompProfile:
      type: RuntimeDefault
  service:
    additionalPorts: []
    annotations: {}
    clusterIP: ""
    externalIPs: []
    externalTrafficPolicy: Cluster
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels: {}
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    nodePort: 30080
    nodePortTls: 30443
    type: ClusterIP
  serviceAccount:
    annotations: {}
    automountServiceAccountToken: true
    create: true
    name: ""
  serviceMonitor:
    additionalLabels: {}
    interval: ""
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    relabelings: []
    sampleLimit: 0
    scrapeTimeout: ""
    selfMonitor: true
    targetLimit: 0
  strategy: {}
  terminationGracePeriodSeconds: 30
  thanosImage:
    registry: quay.io
    repository: thanos/thanos
    sha: ""
    tag: v0.37.2
  thanosRulerInstanceNamespaces: []
  thanosRulerInstanceSelector: ""
  tls:
    enabled: true
    internalPort: 10250
    tlsMinVersion: VersionTLS13
  tolerations: []
  verticalPodAutoscaler:
    controlledResources: []
    enabled: false
    maxAllowed: {}
    minAllowed: {}
    updatePolicy:
      updateMode: Auto
thanosRuler:
  annotations: {}
  enabled: false
  extraSecret:
    annotations: {}
    data: {}
  ingress:
    annotations: {}
    enabled: false
    hosts: []
    labels: {}
    paths: []
    tls: []
  podDisruptionBudget:
    enabled: false
    maxUnavailable: ""
    minAvailable: 1
  route:
    main:
      additionalRules: []
      annotations: {}
      apiVersion: gateway.networking.k8s.io/v1
      enabled: false
      filters: []
      hostnames: []
      httpsRedirect: false
      kind: HTTPRoute
      labels: {}
      matches:
      - path:
          type: PathPrefix
          value: /
      parentRefs: []
  service:
    additionalPorts: []
    annotations: {}
    clusterIP: ""
    enabled: true
    externalIPs: []
    externalTrafficPolicy: Cluster
    ipDualStack:
      enabled: false
      ipFamilies:
      - IPv6
      - IPv4
      ipFamilyPolicy: PreferDualStack
    labels: {}
    loadBalancerIP: ""
    loadBalancerSourceRanges: []
    nodePort: 30905
    port: 10902
    targetPort: 10902
    type: ClusterIP
  serviceAccount:
    annotations: {}
    create: true
    name: ""
  serviceMonitor:
    additionalEndpoints: []
    additionalLabels: {}
    bearerTokenFile: null
    interval: ""
    labelLimit: 0
    labelNameLengthLimit: 0
    labelValueLengthLimit: 0
    metricRelabelings: []
    proxyUrl: ""
    relabelings: []
    sampleLimit: 0
    scheme: ""
    selfMonitor: true
    targetLimit: 0
    tlsConfig: {}
  thanosRulerSpec:
    additionalArgs: []
    additionalConfig: {}
    additionalConfigString: ""
    affinity: {}
    alertDropLabels: []
    alertmanagersConfig:
      existingSecret: {}
      secret: {}
    containers: []
    evaluationInterval: ""
    externalPrefix: null
    externalPrefixNilUsesHelmValues: true
    image:
      registry: quay.io
      repository: thanos/thanos
      sha: ""
      tag: v0.37.2
    initContainers: []
    labels: {}
    listenLocal: false
    logFormat: logfmt
    logLevel: info
    nodeSelector: {}
    objectStorageConfig:
      existingSecret: {}
      secret: {}
    paused: false
    podAntiAffinity: soft
    podAntiAffinityTopologyKey: kubernetes.io/hostname
    podMetadata: {}
    portName: web
    priorityClassName: ""
    queryConfig:
      existingSecret: {}
      secret: {}
    queryEndpoints: []
    replicas: 1
    resources: {}
    retention: 24h
    routePrefix: /
    ruleNamespaceSelector: {}
    ruleSelector: {}
    ruleSelectorNilUsesHelmValues: true
    securityContext:
      fsGroup: 2000
      runAsGroup: 2000
      runAsNonRoot: true
      runAsUser: 1000
      seccompProfile:
        type: RuntimeDefault
    serviceName: null
    storage: {}
    tolerations: []
    topologySpreadConstraints: []
    volumeMounts: []
    volumes: []
    web: {}
windowsMonitoring:
  enabled: false
