#!/bin/bash

echo "🔄 回滚到简单的nginx配置..."

# 应用简单的nginx配置
echo "📋 应用简单nginx配置（无路径重写）..."
kubectl apply -f nginx-config-improved.yaml

# 重启nginx pod
echo "🔄 重启nginx pod..."
kubectl rollout restart deployment/nginx-proxy -n zot

# 等待部署完成
echo "⏳ 等待部署完成..."
kubectl rollout status deployment/nginx-proxy -n zot --timeout=120s

echo ""
echo "✅ 已回滚到简单配置！"
echo ""
echo "📝 回滚后的配置特点："
echo "  ✅ 移除了所有复杂的路径重写规则"
echo "  ✅ 只保留基础的代理功能"
echo "  ✅ 直接将所有请求转发给 zot"
echo "  ✅ 让 zot 自己处理镜像源路由"
echo ""
echo "🧪 现在可以测试基础功能："
echo "  docker pull docker.at9.net/nginx:latest"
echo "  docker pull docker.at9.net/pause:3.9"
echo "  docker pull docker.at9.net/prometheus/prometheus:latest"
echo ""
echo "❌ 注意：完整路径访问将不可用："
echo "  docker pull docker.at9.net/docker.io/nginx:latest  # 这种格式不会工作"
echo "  docker pull docker.at9.net/registry.k8s.io/pause:3.9  # 这种格式不会工作" 